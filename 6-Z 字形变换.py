s = "PAYPALISHIRING"
numRows = 4
length = len(s)
list1=[]
while True:
    if length>=numRows:
        list1.append(numRows)
        length-=numRows
        if length>=numRows-2:
            if numRows-2<=0:
                pass
            else:
                list1.append(numRows-2)
                length-=(numRows-2)
        else:
            if length==0:
                break
            else:
                list1.append(length)
                break
    else:
        if length==0:
            break
        else:
            list1.append(length)
            break
print(list1)
total=0
for i in range(1,len(list1),2):
   total+=list1[i]
for i in range(0,len(list1),2):
    total+=1
print(str(numRows)+'*'+str(total))
# 创建矩阵numRows*total
matrix=[]
for i in range(numRows):
    matrix.append([])
    for j in range(total):
        matrix[i].append([])
# print(matrix)
index=0
for i in list1:
    for item in range(i):
        matrix[0][item].append(s[index])
        index+=1
print(matrix)








# def convert(s, numRows):
#     length = len(s)
#     if numRows == 1:
#         return s
#     elif numRows == 2:
#         return s[::2] + s[1::2]
#     else:
#         result = ""
#         for i in range(numRows):
#             result += s[i::2*numRows-2]
#         return result
    
# print(convert(s, numRows))
    
    
    
    

