import math

# This is the code of integer reversal.
def reverse_integer(x):
    """
    This function takes an integer x and returns its reverse.
    If x is negative, the sign is preserved.
    """
    # Check if the number is negative
    if x < 0:
        sign = -1
    else:
        sign = 1

    # Convert the absolute value of x to a string, reverse it, and convert back to int
    reversed_x = int(str(abs(x))[::-1])

    # Restore the sign
    return sign * reversed_x
result=reverse_integer(120)
print(result)

