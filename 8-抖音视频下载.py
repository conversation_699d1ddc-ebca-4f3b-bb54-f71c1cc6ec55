# from DrissionPage import ChromiumOptions
# path=r"C:\Program Files\Google\Chrome\Application\chrome.exe"
# 配置已保存到文件: D:\py3.13.1\Lib\site-packages\DrissionPage\_configs\configs.ini
# 以后程序可自动从文件加载配置
# ChromiumOptions().set_browser_path(path).save()
from DrissionPage import ChromiumPage
dp = ChromiumPage()
# 打开网页
dp.get("https://www.douyin.com/user/MS4wLjABAAAAB0-gppwu15DtJJZmMpgUqakr7Jw_pmr7skR3IW6MwCQ?from_tab_name=main")
# 等待数据包加载完成
resp=dp.listen.wait()
# 获取响应数据
json_data=resp.response.body
# 打印数据
print(json_data)

