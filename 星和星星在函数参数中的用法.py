# 1.函数参数解包
def multiply(x,y,z):
    return x*y*z
numbers=[2,3,4]
print(multiply(*numbers))
print(multiply(2,3,4))
# 2.合并列表
list1=[1,2,3]
list2=[4,5,6]
combined_list=[*list1,*list2]
print(combined_list)
# 3.字典合并
dict1={'a':1,'b':2}
dict2={'c':3,'d':4}
combined_dict={**dict1,**dict2}
print(combined_dict)
# 4.函数参数解包
def print_info(name,age):
    print(f'姓名:{name},年龄:{age}')
info={'name':'张三','age':18}
print_info(**info)

