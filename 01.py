
import pypinyin


def get_pinyin(char):
    """
    Returns the pinyin of a given Chinese character with tone.
    :param char: A single Chinese character
    :return: The pinyin of the character with tone as a string
    """
    if len(char) != 1:
        raise ValueError("Input must be a single Chinese character")
    pinyin_list = pypinyin.pinyin(char, style=pypinyin.Style.TONE)
    return pinyin_list[0][0] if pinyin_list else ""


print(get_pinyin('你'))  # returns 'nǐ'
print(get_pinyin('好'))  # returns 'hǎo'