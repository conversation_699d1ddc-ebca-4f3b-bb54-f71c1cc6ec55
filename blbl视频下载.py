import requests
import os
import re

import requests

cookies = {
    'enable_web_push': 'DISABLE',
    'home_feed_column': '5',
    'header_theme_version': 'CLOSE',
    'CURRENT_BLACKGAP': '0',
    'is-2022-channel': '1',
    'SESSDATA': 'd4ee9665%2C1750046798%2C5ac6b%2Ac1CjABoMD1y-tvwdkGviWAkdcSZdIGgUpH6wQ9qFN6UafYp-bd1x1ah9ltWD_BiQyrzEESVnk5dW55UUlsS1h4dkNpT0VqejdZUGRPam9CRjFhRmxqbU9lZmhNaXM3dmZjcnpVNnpkbmlmLWd6WXBiM0tLVGtuMVBGVzJfaGttSl9nLWl0QXVibjBRIIEC',
    'bili_jct': '97dbbd36c5277a4e6ecfd8e87f0f8453',
    'DedeUserID': '348492634',
    'DedeUserID__ckMd5': 'fedcf26ea37e9287',
    'sid': '6nde5m1c',
    'buvid3': '4E9C4F7E-4779-89DE-0970-7FA00A1ADAA577891infoc',
    'b_nut': '1736685377',
    '_uuid': '3F658C8C-24BE-64E4-BD3E-3C105AC210ECF178560infoc',
    'CURRENT_QUALITY': '80',
    'buvid_fp': '4a6fc7f8671e26a2776ab822adffbf08',
    'rpdid': '0zbfVFTL7M|XDj5BeX0|32p|3w1TFavg',
    'enable_feed_channel': 'ENABLE',
    'buvid4': '086A3B00-DF8F-C093-2644-54D8183EA1E160103-023122411-vG2tS%2BrKKrtfLF0UpFSgiA%3D%3D',
    'b_lsid': 'CF258B107_196A55FE9EB',
    'bili_ticket': 'eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDY3OTA0NTAsImlhdCI6MTc0NjUzMTE5MCwicGx0IjotMX0.g9_KuMllfTXfaS5GmxVWpIPhwWdtzqrsOQV62cRO8Ng',
    'bili_ticket_expires': '1746790390',
    'bmg_af_switch': '1',
    'bmg_src_def_domain': 'i0.hdslb.com',
    'browser_resolution': '1651-843',
    'CURRENT_FNVAL': '4048',
    'bp_t_offset_348492634': '1063836680227651584',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,ja;q=0.8',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=0, i',
    'referer': 'https://search.bilibili.com/all?keyword=%E6%8B%BF%E4%BB%80%E4%B9%88%E6%8A%B5%E6%8A%97%E6%88%91%E5%95%8A%E8%87%AD%E5%BC%9F%E5%BC%9F&from_source=webtop_search&spm_id_from=333.1007&search_source=5',
    'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
    # 'cookie': 'enable_web_push=DISABLE; home_feed_column=5; header_theme_version=CLOSE; CURRENT_BLACKGAP=0; is-2022-channel=1; SESSDATA=d4ee9665%2C1750046798%2C5ac6b%2Ac1CjABoMD1y-tvwdkGviWAkdcSZdIGgUpH6wQ9qFN6UafYp-bd1x1ah9ltWD_BiQyrzEESVnk5dW55UUlsS1h4dkNpT0VqejdZUGRPam9CRjFhRmxqbU9lZmhNaXM3dmZjcnpVNnpkbmlmLWd6WXBiM0tLVGtuMVBGVzJfaGttSl9nLWl0QXVibjBRIIEC; bili_jct=97dbbd36c5277a4e6ecfd8e87f0f8453; DedeUserID=348492634; DedeUserID__ckMd5=fedcf26ea37e9287; sid=6nde5m1c; buvid3=4E9C4F7E-4779-89DE-0970-7FA00A1ADAA577891infoc; b_nut=1736685377; _uuid=3F658C8C-24BE-64E4-BD3E-3C105AC210ECF178560infoc; CURRENT_QUALITY=80; buvid_fp=4a6fc7f8671e26a2776ab822adffbf08; rpdid=0zbfVFTL7M|XDj5BeX0|32p|3w1TFavg; enable_feed_channel=ENABLE; buvid4=086A3B00-DF8F-C093-2644-54D8183EA1E160103-023122411-vG2tS%2BrKKrtfLF0UpFSgiA%3D%3D; b_lsid=CF258B107_196A55FE9EB; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDY3OTA0NTAsImlhdCI6MTc0NjUzMTE5MCwicGx0IjotMX0.g9_KuMllfTXfaS5GmxVWpIPhwWdtzqrsOQV62cRO8Ng; bili_ticket_expires=1746790390; bmg_af_switch=1; bmg_src_def_domain=i0.hdslb.com; browser_resolution=1651-843; CURRENT_FNVAL=4048; bp_t_offset_348492634=1063836680227651584',
}

params = {
    'spm_id_from': '333.337.search-card.all.click',
    'vd_source': '52c08d28746d49396a8dd0b42aaec228',
}

response = requests.get(url='https://www.bilibili.com/video/BV14m4y1j7D8/',
                        params=params, cookies=cookies, headers=headers)
print(response.text)
video_url = re.findall(
    '"video":(.*?)"baseUrl":"(.*?)","base_url"', response.text)[0][1]
audio_url = re.findall(
    '"audio".*?"baseUrl":"(.*?)","base_url"', response.text)[0]
title = re.findall(
    '<title data-vue-meta="true">(.*?)</title>', response.text)[0]
print('-'*50)
print('video_url:' + video_url)
print('audio_url:'+audio_url)
print('title:'+title)
video_response=requests.get(url=video_url, headers=headers,params=params, cookies=cookies)
audio_response=requests.get(url=audio_url, headers=headers,params=params, cookies=cookies)
with open('video.mp4', 'wb') as f:
    f.write(video_response.content)
with open('audio.mp3', 'wb') as f:
    f.write(audio_response.content)
video_path = 'video.mp4'
audio_path = 'audio.mp3'
hc_path = f'{title}.mp4'
os.system(f'ffmpeg -i {video_path} -i {audio_path} -c:v copy -c:a copy {hc_path}')
