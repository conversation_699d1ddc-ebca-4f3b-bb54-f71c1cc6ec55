import datetime
d=datetime.datetime.now()
d1=datetime.datetime(2025,4,10)
print(d.strftime('%Y-%m-%d'))
print(d1.strftime('%Y-%m-%d'))
print(d.strftime("%A")) 
print(d.strftime("%Y-%m-%d %H:%M:%S"))
def add_days(date,days):
    return date+datetime.timedelta(days=days)
d2=add_days(d,10)
print(d2.strftime('%Y-%m-%d'))
class Person:
    def __init__(self,name,age):
        self.name=name
        self.age=age
    def __str__(self):
        return '姓名：%s,年龄：%s'%(self.name,self.age)
p=Person('张三',18)
print(p)
print(str(p))
print(repr(p))
