# def func(x,y=0,*args,**kwargs):
#     result = x + y
#     result += sum(args)
#     result += sum(kwargs.values())
#     return result
# print(func(1,2,3,4,a=1,b=2))

# class A:
#     def __init__(self,x):
#         self.x=1
#     def __getattr__(self, name):
#         return f"Attribute {name} not found"
# a=A(1)
# print(a.x)  
# print(a.y) 

# decorator
def decorator(func):
    def wrapper(*args,**kwargs):
        print("开始") 
        result = func(*args,**kwargs)
        print("结束")
        return result
    return wrapper

#@decorator  等介于hello=decorator("您好")
@decorator
def hello():
    print("您好")
hello()
