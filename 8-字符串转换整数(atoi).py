def atoi(s):
    """
    Convert a string to an integer.
    :param s: The input string.
    :return: The converted integer.
    """
    s = s.strip()  # Remove leading and trailing whitespace
    if not s:
        return 0

    sign = 1
    index = 0
    if s[index] == '-':
        sign = -1
        index += 1
    elif s[index] == '+':
        index += 1

    result = 0
    while index < len(s) and s[index].isdigit():
        digit = int(s[index])
        result = result * 10 + digit
        index += 1

    return sign * result
# Example usage:
print(atoi("-042"))  # Output: -42
print(atoi("+42"))
print(atoi("4193 with words"))  # Output: 4193
print(atoi("   -42"))  # Output: -42
print(atoi("4193"))  # Output: 4193
print(atoi("0000000000012345678"))  # Output: 12345678